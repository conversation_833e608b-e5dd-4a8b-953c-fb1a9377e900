import * as React from "react"
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  VisibilityState,
  ColumnFiltersState,
  SortingState,
  RowSelectionState,
} from "@tanstack/react-table"
import { cn } from "@/lib"
// Note: Using native HTML elements instead of shadcn/ui Table components
// to avoid double overflow wrapper that breaks sticky positioning
import { Checkbox } from "./checkbox"
import { Button } from "./button"
import { Input } from "./input"
import {
  ChevronDown,
  ChevronUp,
  ChevronsUpDown,
  Search,
  Filter,
  Download
} from "lucide-react"

// Advanced table interfaces based on TanStack Table and proven patterns
export interface GroupHeaderConfig {
  label: string
  columns: string[]
  className?: string
}

export interface FrozenColumnsConfig {
  count: number
  shadowIntensity?: 'light' | 'medium' | 'heavy'
}

export interface SelectionConfig<T = any> {
  enabled: boolean
  mode?: 'single' | 'multiple'
  selectedRows?: T[]
  onSelectionChange?: (rows: T[]) => void
  getRowId?: (row: T) => string | number
}

export interface MobileConfig {
  enabled: boolean
  hideColumns?: string[]
  stackColumns?: boolean
  touchOptimized?: boolean
}

export interface AdvancedDataTableProps<T = any> {
  data: T[]
  columns: ColumnDef<T>[]
  selection?: SelectionConfig<T>
  groupHeaders?: GroupHeaderConfig[]
  frozenColumns?: FrozenColumnsConfig
  mobile?: MobileConfig
  searchable?: boolean
  filterable?: boolean
  sortable?: boolean
  pagination?: boolean
  pageSize?: number
  className?: string
  onRowClick?: (row: T) => void
  loading?: boolean
  emptyMessage?: string
  exportable?: boolean
  onExport?: (data: T[]) => void
}

// Helper function to generate group headers from column definitions
export function generateGroupHeaders<T>(
  columns: ColumnDef<T>[],
  groupConfigs: GroupHeaderConfig[]
): { label: string; colSpan: number; startIndex: number }[] {
  const groupHeaders: { label: string; colSpan: number; startIndex: number }[] = []
  
  groupConfigs.forEach(config => {
    const startIndex = columns.findIndex(col => 
      config.columns.includes((col as any).accessorKey || (col as any).id)
    )
    if (startIndex !== -1) {
      groupHeaders.push({
        label: config.label,
        colSpan: config.columns.length,
        startIndex
      })
    }
  })
  
  return groupHeaders
}

// Helper function to check if column is frozen (based on reference implementation)
function isFrozenColumn(columnIndex: number, frozenCount: number, hasSelection: boolean): boolean {
  const adjustedIndex = hasSelection ? columnIndex + 1 : columnIndex
  return adjustedIndex < frozenCount
}

// Helper function to get frozen column left position (simplified for debugging)
function getFrozenLeft(
  columnIndex: number,
  frozenCount: number,
  hasSelection: boolean,
  tableColumns: any[]
): number {
  if (!isFrozenColumn(columnIndex, frozenCount, hasSelection)) return 0

  // Simplified calculation for debugging
  if (hasSelection) {
    if (columnIndex === 0) return 0      // Selection column
    if (columnIndex === 1) return 40     // Name column after selection
  } else {
    if (columnIndex === 0) return 0      // First column
    if (columnIndex === 1) return 150    // Second column
  }

  return 0
}

// Get frozen column CSS class
function getFrozenColumnClass(
  columnIndex: number,
  frozenCount: number,
  hasSelection: boolean
): string {
  const isFrozen = isFrozenColumn(columnIndex, frozenCount, hasSelection)

  console.log(`Column ${columnIndex}: isFrozen=${isFrozen}, frozenCount=${frozenCount}, hasSelection=${hasSelection}`)

  if (!isFrozen) return ''

  // Return specific CSS class for each frozen column
  const className = `frozen-column-${columnIndex}`
  console.log(`Applying class: ${className}`)
  return className
}

// Main AdvancedDataTable component
export function AdvancedDataTable<T = any>({
  data,
  columns,
  selection,
  groupHeaders = [],
  frozenColumns,
  mobile,
  searchable = true,
  filterable = false,
  sortable = true,
  pagination = true,
  pageSize = 10,
  className,
  onRowClick,
  loading = false,
  emptyMessage = "No data available",
  exportable = false,
  onExport
}: AdvancedDataTableProps<T>) {
  // Table ref for dynamic width calculation
  const tableRef = React.useRef<HTMLTableElement>(null)

  // Table state
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({})
  const [globalFilter, setGlobalFilter] = React.useState("")

  // Note: Simplified frozen column implementation without dynamic width tracking

  // Mobile responsive column hiding
  React.useEffect(() => {
    if (mobile?.enabled && mobile.hideColumns) {
      const hiddenColumns: VisibilityState = {}
      mobile.hideColumns.forEach(columnId => {
        hiddenColumns[columnId] = false
      })
      setColumnVisibility(hiddenColumns)
    }
  }, [mobile])

  // Prepare columns with selection if enabled
  const tableColumns = React.useMemo(() => {
    const cols = [...columns]

    if (selection?.enabled) {
      const selectionColumn: ColumnDef<T> = {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={table.getIsAllPageRowsSelected()}
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Select all"
            className="translate-y-[2px]"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
            className="translate-y-[2px]"
          />
        ),
        enableSorting: false,
        enableHiding: false,
        size: 40,
      }
      cols.unshift(selectionColumn)
    }

    return cols
  }, [columns, selection?.enabled])

  // Initialize table
  const table = useReactTable({
    data,
    columns: tableColumns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: pagination ? getPaginationRowModel() : undefined,
    getSortedRowModel: sortable ? getSortedRowModel() : undefined,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: "includesString",
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
      pagination: pagination ? { pageIndex: 0, pageSize } : undefined,
    },
    initialState: {
      pagination: pagination ? { pageIndex: 0, pageSize } : undefined,
    },
  })

  // Handle selection changes
  React.useEffect(() => {
    if (selection?.enabled && selection.onSelectionChange) {
      const selectedRows = table.getFilteredSelectedRowModel().rows.map(row => row.original)
      selection.onSelectionChange(selectedRows)
    }
  }, [rowSelection, selection, table])

  // Generate computed group headers
  const computedGroupHeaders = React.useMemo(() => {
    return generateGroupHeaders(tableColumns, groupHeaders)
  }, [tableColumns, groupHeaders])

  // Export functionality
  const handleExport = () => {
    if (onExport) {
      const selectedRows = table.getFilteredSelectedRowModel().rows
      const dataToExport = selectedRows.length > 0
        ? selectedRows.map(row => row.original)
        : table.getFilteredRowModel().rows.map(row => row.original)
      onExport(dataToExport)
    }
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Toolbar */}
      {(searchable || exportable || filterable) && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {searchable && (
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search..."
                  value={globalFilter ?? ""}
                  onChange={(event) => setGlobalFilter(String(event.target.value))}
                  className="pl-8 max-w-sm"
                />
              </div>
            )}
            {filterable && (
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {exportable && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleExport}
                disabled={loading}
              >
                <Download className="mr-2 h-4 w-4" />
                Export {table.getFilteredSelectedRowModel().rows.length > 0
                  ? `(${table.getFilteredSelectedRowModel().rows.length})`
                  : ''}
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Table Container */}
      <div className="rounded-md border bg-[var(--bg-primary)]">
        <div className="relative w-full overflow-auto" style={{ maxHeight: '400px' }}>
          <table
            ref={tableRef}
            className="w-full caption-bottom text-sm bg-transparent text-foreground"
            style={{ minWidth: '800px' }}
          >
            <thead className="[&_tr]:border-b">
              {/* Group Headers */}
              {computedGroupHeaders.length > 0 && (
                <tr className="bg-[var(--bg-header-group)] border-b border-[var(--border-color)]">
                  {selection?.enabled && (
                    <th className="w-[40px] h-12 px-4 text-left align-middle font-medium text-muted-foreground frozen-column-0" />
                  )}
                  {computedGroupHeaders.map((group, index) => (
                    <th
                      key={`group-${index}`}
                      colSpan={group.colSpan}
                      className="h-12 px-4 text-center align-middle font-semibold text-[var(--text-header)] bg-[var(--bg-header-group)]"
                    >
                      {group.label}
                    </th>
                  ))}
                </tr>
              )}

              {/* Column Headers */}
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id} className="bg-[var(--bg-header)] border-b border-[var(--border-color)]">
                  {headerGroup.headers.map((header, columnIndex) => {
                    const frozenClass = frozenColumns ? getFrozenColumnClass(
                      columnIndex,
                      frozenColumns.count,
                      selection?.enabled || false
                    ) : ''

                    return (
                      <th
                        key={header.id}
                        className={cn(
                          "h-12 px-4 text-left align-middle font-medium text-[var(--text-header)] bg-[var(--bg-header)]",
                          frozenClass
                        )}
                      >
                        {header.isPlaceholder ? null : (
                          <div
                            className={cn(
                              "flex items-center space-x-2",
                              header.column.getCanSort() && "cursor-pointer select-none"
                            )}
                            onClick={header.column.getToggleSortingHandler()}
                          >
                            <span>
                              {flexRender(header.column.columnDef.header, header.getContext())}
                            </span>
                            {header.column.getCanSort() && (
                              <span className="ml-2">
                                {header.column.getIsSorted() === "desc" ? (
                                  <ChevronDown className="h-4 w-4" />
                                ) : header.column.getIsSorted() === "asc" ? (
                                  <ChevronUp className="h-4 w-4" />
                                ) : (
                                  <ChevronsUpDown className="h-4 w-4" />
                                )}
                              </span>
                            )}
                          </div>
                        )}
                      </th>
                    )
                  })}
                </tr>
              ))}
            </thead>

            <tbody className="[&_tr:last-child]:border-0">
              {loading ? (
                <tr>
                  <td
                    colSpan={table.getVisibleFlatColumns().length}
                    className="p-4 align-middle text-center py-8 text-[var(--text-secondary)]"
                  >
                    Loading...
                  </td>
                </tr>
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <tr
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                    className={cn(
                      "border-b transition-colors hover:bg-[var(--hover-bg)] cursor-pointer",
                      row.getIsSelected() && "bg-[var(--hover-bg)]",
                      mobile?.touchOptimized && "min-h-[44px]"
                    )}
                    onClick={() => onRowClick?.(row.original)}
                  >
                    {row.getVisibleCells().map((cell, columnIndex) => {
                      const frozenClass = frozenColumns ? getFrozenColumnClass(
                        columnIndex,
                        frozenColumns.count,
                        selection?.enabled || false
                      ) : ''

                      return (
                        <td
                          key={cell.id}
                          className={cn(
                            "p-4 align-middle text-[var(--text-primary)]",
                            frozenClass
                          )}
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </td>
                      )
                    })}
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={table.getVisibleFlatColumns().length}
                    className="p-4 align-middle text-center py-8 text-[var(--text-secondary)]"
                  >
                    {emptyMessage}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {pagination && (
        <div className="flex items-center justify-between px-2">
          <div className="flex-1 text-sm text-muted-foreground">
            {table.getFilteredSelectedRowModel().rows.length} of{" "}
            {table.getFilteredRowModel().rows.length} row(s) selected.
          </div>
          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">Rows per page</p>
              <select
                value={table.getState().pagination.pageSize}
                onChange={(e) => {
                  table.setPageSize(Number(e.target.value))
                }}
                className="h-8 w-[70px] rounded border border-input bg-background px-3 py-1 text-sm"
                aria-label="Rows per page"
              >
                {[10, 20, 30, 40, 50].map((pageSize) => (
                  <option key={pageSize} value={pageSize}>
                    {pageSize}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Page {table.getState().pagination.pageIndex + 1} of{" "}
              {table.getPageCount()}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                <ChevronDown className="h-4 w-4 rotate-90" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                <ChevronDown className="h-4 w-4 -rotate-90" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}


